targetTemplates:
  Application:
    platform: iOS
    postBuildScripts:
    - basedOnDependencyAnalysis: false
      name: Update Build Number
      path: .bin/update-build-number.sh
      shell: /bin/bash
    - basedOnDependencyAnalysis: false
      name: Copy GoogleService-Info.plist to app
      script: "GOOGLESERVICE_INFO_PLIST=\"GoogleService-Info.plist\"\nGOOGLESERVICE_INFO_Alpha=\"Apps/${TARGET_NAME}/Firebase/alpha/GoogleService-Info.plist\"\nGOOGLESERVICE_INFO_Beta=\"Apps/${TARGET_NAME}/Firebase/beta/GoogleService-Info.plist\"\nGOOGLESERVICE_INFO_Release=\"Apps/${TARGET_NAME}/Firebase/release/GoogleService-Info.plist\"\nPLIST_DESTINATION=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app\"\nif
        [ \"${CONFIGURATION}\" == \"Alpha (Debug)\" ]; then\n\t\tcp \"$(eval echo
        \"\\$GOOGLESERVICE_INFO_Alpha\")\" \"${PLIST_DESTINATION}/${GOOGLESERVICE_INFO_PLIST}\"\n\t\techo
        \"\U0001F9F0 Copied $(eval echo \"\\$GOOGLESERVICE_INFO_Alpha\") to ${PLIST_DESTINATION}/${GOOGLESERVICE_INFO_PLIST}\"\n\t\texit
        0\nfi\n\nfor ENVIRONMENT in \"Alpha\" \"Beta\" \"Release\"; do\n\t\techo \"Checking
        ${ENVIRONMENT} ${GOOGLESERVICE_INFO_PLIST}...\"\n\t\tif [ ! -f \"$(eval echo
        \"\\$GOOGLESERVICE_INFO_${ENVIRONMENT}\")\" ]; then\n\t\t\techo \"\u274C ${ENVIRONMENT}
        GoogleService-Info.plist not found. Please ensure it can be found at $(eval
        echo \"\\$GOOGLESERVICE_INFO_${ENVIRONMENT}\").\"\n\t\t\texit -1\n\t\tfi\n\tif
        [ \"${CONFIGURATION}\" == \"${ENVIRONMENT}\" ]; then\n\t\tcp \"$(eval echo
        \"\\$GOOGLESERVICE_INFO_${ENVIRONMENT}\")\" \"${PLIST_DESTINATION}/${GOOGLESERVICE_INFO_PLIST}\"\n\t\techo
        \"\U0001F9F0 Copied $(eval echo \"\\$GOOGLESERVICE_INFO_${ENVIRONMENT}\")
        to ${PLIST_DESTINATION}/${GOOGLESERVICE_INFO_PLIST}\"\n\tfi\ndone"
      shell: /bin/sh
    - basedOnDependencyAnalysis: false
      inputFiles:
      - ${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}
      - ${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${PRODUCT_NAME}
      - ${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist
      - $(BUILT_PRODUCTS_DIR)/$(UNLOCALIZED_RESOURCES_FOLDER_PATH)/GoogleService-Info.plist
      - $(BUILT_PRODUCTS_DIR)/$(EXECUTABLE_PATH)
      name: Upload symbols for Crashlytics
      path: .bin/upload-symbols-for-crashlytics.sh
      shell: /bin/bash
    preBuildScripts:
    - inputFiles:
      - Apps/${target_name}/Secrets.ogkeystore
      name: Generate Secrets Keystore
      outputFiles:
      - Apps/${target_name}/Sources/CodeGenerated/Secrets.generated.swift
      script: "if [ $ACTION != \u201Cindexbuild\u201D ]; then\n  cd ${SRCROOT}/.bin/\n
        \ ./secret-service ${SRCROOT}/Apps/${target_name}/Sources/CodeGenerated/Secrets.generated.swift
        --env-path ${SRCROOT}/.env-vars --keystore-path ${SRCROOT}/Apps/${target_name}/Secrets.ogkeystore\nfi"
      shell: /bin/sh
    - name: Generate UICatalog
      script: "if [ $ACTION != \u201Cindexbuild\u201D ]; then\n./.bin/sourcery --sources
        ${SRCROOT}/Packages/UICatalog/Sources/UICatalog \t--templates ${SRCROOT}/Packages/UICatalog/Sources/Templates
        \t--output ${SRCROOT}/Packages/UICatalog/Sources/UICatalog/CodeGenerated\nfi"
      shell: /bin/sh
    settings:
      base:
        CODE_SIGN_ENTITLEMENTS: Apps/${target_name}/app.entitlements
        INFOPLIST_FILE: Apps/${target_name}/Info.plist
        OTHER_LDFLAGS: $(inherited) -ObjC
    sources:
    - path: Apps/${target_name}/Config
    - buildPhase: none
      path: Apps/${target_name}/Firebase
    - buildPhase: none
      path: Apps/${target_name}/Firebase/alpha
    - buildPhase: none
      path: Apps/${target_name}/Firebase/beta
    - buildPhase: none
      path: Apps/${target_name}/Firebase/release
    - path: Apps/${target_name}/Resources
    - path: Apps/${target_name}/Sources
    - buildPhase: none
      path: Apps/${target_name}/Info.plist
    - buildPhase: none
      path: Apps/${target_name}/app.entitlements
    - optional: true
      path: Apps/${target_name}/Sources/CodeGenerated/Secrets.generated.swift
    - path: Shared/Sources
    templates:
    - SwiftLintable
    type: application
  NotificationService:
    dependencies:
    - package: Airship
      product: AirshipNotificationServiceExtension
    platform: iOS
    postBuildScripts:
    - basedOnDependencyAnalysis: false
      name: Update Build Number
      path: .bin/update-build-number.sh
      shell: /bin/bash
    settings:
      base:
        INFOPLIST_FILE: Shared/NotificationService/Info.plist
        OTHER_LDFLAGS: $(inherited) -ObjC
    sources:
    - path: Shared/NotificationService/Sources
    - buildPhase: none
      path: Shared/NotificationService/Info.plist
    type: app-extension
  SwiftLintable:
    postCompileScripts:
    - basedOnDependencyAnalysis: false
      name: Lint project
      script: ./.bin/swiftlint
      shell: /bin/sh
  Tests:
    dependencies:
    - target: ${targetID}
    platform: iOS
    settings:
      base:
        INFOPLIST_FILE: Apps/${targetID}-Tests/Info.plist
    sources:
    - name: ${targetID}-Tests
      path: Apps/${targetID}-Tests/Sources
    - buildPhase: none
      path: Apps/${targetID}-Tests/Info.plist
    type: bundle.unit-test
