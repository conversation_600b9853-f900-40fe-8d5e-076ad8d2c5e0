name: Build iOS
name: Build and Deploy iOS

on:
  push:
   branches: 
    - main
    - 'epic/**'
    
  pull_request:
   branches: 
    - main
    - 'epic/**'

  workflow_dispatch:
    inputs:
      target:
        description: "App"
        required: true
        type: choice
        options:
        - BON
        - BPX
        - CRE
        - HEI
        - LAS
        - MAN
        - SAN
        - SHE
        - WIT
        - YLFLNL
        - YLFLSE
      config:
        description: "Build"
        required: true
        type: choice
        options:
        - Alpha
        - Beta
        - Release

jobs:
  ios-deployment:
    name: iOS Deployment
    runs-on: macos-15
    steps:
      - id: xcode-setup
        name: Setup Xcode version
        uses: maxim-lobanov/setup-xcode@v1.6.0
        with:
          xcode-version: '16.4'
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Setting fetch_depth to 0 provides the whole project history and enables comparison between latest tag ad head used for versioning and commit history
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.1
          bundler-cache: true
      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.OGKIT_SSH_PRIVATE_KEY }}
          name: id_rsa # optional
          known_hosts: ${{ secrets.KNOWN_HOSTS_OGKIT }}
          if_key_exists: fail # replace / ignore / fail; optional (defaults to fail)
      - name: Cache SPMs
        uses: actions/cache@v3
        with:
          path: .build
          key: ${{ runner.os }}-spm-${{ hashFiles('**/Package.resolved') }}
          restore-keys: |
            ${{ runner.os }}-spm-          
      - name: make deploy
        run: |
          make deploy target=${{ github.event.inputs.target }} config=${{ github.event.inputs.config }}
    env:
      BUNDLE_PATH: .vendor/bundle
      SAUCE_USERNAME: ${{ secrets.SAUCE_USERNAME }}
      SAUCE_ACCESS_KEY: ${{ secrets.SAUCE_ACCESS_KEY }}
      SECRET_KEYCHAIN_PASSWORD: ${{ secrets.SECRET_KEYCHAIN_PASSWORD }}
      SECRET_KEYSTORE_PASSWORD: ${{ secrets.SECRET_KEYSTORE_PASSWORD }}
      FASTLANE_PASSWORD: ${{ secrets.FASTLANE_PASSWORD }}
      FASTLANE_USER: ${{ secrets.FASTLANE_USER }}
      FIREBASE_CLI_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
      MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
      MATCH_KEYCHAIN_PASSWORD: ${{ secrets.MATCH_KEYCHAIN_PASSWORD }}
      MATCH_GIT_BASIC_AUTHORIZATION: ${{ secrets.MATCH_GIT_BASIC_AUTHORIZATION }}
      SLACK_URL: ${{ secrets.SLACK_HOOK_URL }}
      FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}      
      CI_GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}
      TERM: xterm-256color
      HOMEBREW_NO_INSTALL_CLEANUP: TRUE
      FASTLANE_DONT_STORE_PASSWORD: 1
      FASTLANE_HIDE_CHANGELOG: true
      FASTLANE_SKIP_UPDATE_CHECK: true
      FASTLANE_XCODE_LIST_TIMEOUT: 60
      FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT: 60
