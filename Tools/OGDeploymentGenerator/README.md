# OGDeploymentGenerator

A powerful command-line tool that automates the creation of iOS Xcode projects with complete CI/CD pipeline setup. The `OGDeploymentGenerator` streamlines iOS app development by generating all necessary project files, build configurations, and deployment workflows from simple YAML configuration files.

## What It Does

The tool automates the creation of:
- **Xcode Project Files**: Generates complete `.xcodeproj` using XcodeGen with proper targets, schemes, and build settings
- **CI/CD Workflows**: Creates GitHub Actions workflows for automated testing, building, and deployment
- **Fastlane Configuration**: Generates Fastfile and Appfile for streamlined iOS app deployment
- **Build Scripts**: Sets up pre-build and post-build scripts for secrets management, versioning, and Firebase integration
- **Project Structure**: Creates organized directory structure with proper separation of apps, packages, and shared code

## Key Features

- 🚀 **Single Configuration**: Define all your apps and their configurations in one `Apps.yml` file
- 🔧 **Flexible Architecture**: Support for both shared framework and shared source folder approaches
- 📱 **Multi-App Support**: Generate multiple app targets from a single project configuration
- 🔐 **Secrets Management**: Built-in support for secure secrets handling via Keychain or Keystore
- 🎨 **Customizable Templates**: Use default templates or provide your own Stencil templates
- 🔄 **CI/CD Ready**: Automatic generation of GitHub Actions workflows and Fastlane configurations
- 📦 **Package Management**: Automatic discovery and integration of local Swift packages

## Table of Contents

- [OGDeploymentGenerator](#ogdeploymentgenerator)
  - [What It Does](#what-it-does)
  - [Key Features](#key-features)
  - [Table of Contents](#table-of-contents)
  - [Quick Start](#quick-start)
  - [Usage](#usage)
    - [Command Syntax](#command-syntax)
    - [Options Reference](#options-reference)
    - [Common Usage Patterns](#common-usage-patterns)
  - [Configuration Files](#configuration-files)
    - [Apps.yml File](#appsyml-file)
    - [Dependencies.yml File](#dependenciesyml-file)
    - [Packages.yml File](#packagesyml-file)
  - [Build Scripts](#build-scripts)
    - [Pre-Build Scripts](#pre-build-scripts)
    - [Post-Build Scripts](#post-build-scripts)
    - [Secrets Generation](#secrets-generation)
  - [Custom Templates](#custom-templates)
  - [Examples](#examples)
  - [Build and Installation](#build-and-installation)
  - [Troubleshooting](#troubleshooting)

## Quick Start

1. **Create your Apps.yml configuration file**:
```yaml
MyApp:
  name: My Awesome App
  version: 1.0.0
  configs:
    Alpha (Debug):
      DEVELOPMENT_TEAM: YOUR_TEAM_ID
      PRODUCT_BUNDLE_IDENTIFIER: com.yourcompany.myapp.dev
    Beta:
      DEVELOPMENT_TEAM: YOUR_TEAM_ID
      PRODUCT_BUNDLE_IDENTIFIER: com.yourcompany.myapp.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Your Company'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.yourcompany.myapp.beta
```

2. **Run the generator**:
```bash
og-deployment-generator --project-path /path/to/your/project --project-name MyAwesomeApp
```

3. **Open the generated project**:
The tool automatically opens the generated `.xcodeproj` file in Xcode (unless `--do-not-open-project` is specified).

## Usage

### Command Syntax

```bash
og-deployment-generator [OPTIONS]
```

### Options Reference

#### Project Configuration
| Option | Description | Default |
|--------|-------------|---------|
| `--project-path <path>` | Absolute path to your iOS project root directory | Current directory |
| `--project-name <name>` | Name for the generated Xcode project | `OttoGroup` |
| `--apps-yml-path <path>` | Relative path to Apps.yml configuration file | `Apps.yml` |
| `--target <name>` | Build only a specific app target (e.g., `WIT`) | All targets |

#### Directory Structure
| Option | Description | Default |
|--------|-------------|---------|
| `--apps-dir <path>` | Directory containing individual app target folders | `Apps` |
| `--packages-path <path>` | Directories containing local Swift packages (repeat for multiple paths) | `Packages`, `OGKit` |
| `--templates-dir <path>` | Custom Stencil templates directory | Built-in templates |
| `--initial-files-path <path>` | Template files for new app targets | None |

#### Build Configuration
| Option | Description | Default |
|--------|-------------|---------|
| `--xcode-version <version>` | Target Xcode version (e.g., `16.4`) | `16.4` |
| `--ios-version <version>` | Minimum iOS deployment target | `15.0` |
| `--swift-version <version>` | Swift language version | `5.9` |
| `--minimum-xcode-gen-version <version>` | Minimum XcodeGen version required | `2.38.0` |

#### CI/CD and Deployment
| Option | Description | Default |
|--------|-------------|---------|
| `--github-workflows-dir <path>` | GitHub Actions workflows directory | `.github/workflows` |
| `--fastlane-dir <path>` | Fastlane configuration directory | `fastlane` |
| `--firebase-info-plist-dir <path>` | Firebase configuration files directory | `Firebase` |
| `--app-icon-set-path <path>` | App icon asset catalog path | `Resources/Assets.xcassets/AppIcon.appiconset` |

#### Build Scripts and Secrets
| Option | Description | Default |
|--------|-------------|---------|
| `--secrets-generation-type <type>` | Secrets storage method: `keychain` or `keystore` | `keystore` |
| `--pre-build-scripts <scripts>` | Pre-build scripts to execute | `generateSecretsKeychain,generateUICatalog` |
| `--post-build-scripts <scripts>` | Post-build scripts to execute | `updateBuildNumber,copyGoogleServiceInfoPlist,uploadSymbolsForCrashlytics` |

#### Configuration Files
| Option | Description | Default |
|--------|-------------|---------|
| `--dependencies-yml <path>` | External dependencies configuration file | `dependencies.yml` |
| `--packages-yml <path>` | Local packages configuration file | `packages.yml` |

#### Architecture Options
| Option | Description | Default |
|--------|-------------|---------|
| `--use-framework` | Use shared framework instead of shared folder | `false` |
| `--shared-framework-name <name>` | Name for shared framework (when using `--use-framework`) | `OGShared` |

#### Development Options
| Option | Description | Default |
|--------|-------------|---------|
| `--keep-generated-yml` | Preserve intermediate YAML files for debugging | `false` |
| `--do-not-open-project` | Skip opening Xcode after generation | `false` |
| `--generate-swift-app-file` | Create basic SwiftUI App struct for new targets | `false` |

> **Note on Multiple Paths**: For options that accept multiple values (like `--packages-path`), repeat the option for each path:
> ```bash
> --packages-path Packages --packages-path OGKit --packages-path Libraries
> ```

### Common Usage Patterns

#### Basic Project Generation
```bash
# Generate project with default settings
og-deployment-generator --project-path /path/to/project --project-name MyApp

# Generate specific target only
og-deployment-generator --project-path /path/to/project --target MySpecificApp
```

#### Custom Configuration
```bash
# Use custom templates and configuration files
og-deployment-generator \
  --project-path /path/to/project \
  --project-name MyApp \
  --templates-dir CustomTemplates \
  --apps-yml-path config/MyApps.yml \
  --dependencies-yml config/deps.yml

# Multiple package paths
og-deployment-generator \
  --project-path /path/to/project \
  --project-name MyApp \
  --packages-path Packages \
  --packages-path OGKit \
  --packages-path Libraries \
  --packages-path SharedComponents
```

#### Framework Architecture
```bash
# Use shared framework architecture
og-deployment-generator \
  --project-path /path/to/project \
  --use-framework \
  --shared-framework-name MySharedFramework
```

#### CI/CD Focused
```bash
# Generate for automated environments
og-deployment-generator \
  --project-path /path/to/project \
  --do-not-open-project \
  --keep-generated-yml \
  --secrets-generation-type keystore
```

## Configuration Files

### Apps.yml File

The `Apps.yml` file is the heart of your project configuration. It defines all your app targets, their display names, versions, and build configurations for different environments.

#### Structure

```yaml
# App Target Key (used as folder name and target identifier)
AppTargetName:
  # Human-readable app name
  name: Display Name
  # App version (used for marketing version)
  version: X.Y.Z
  # Build configurations for different environments
  configs:
    # Configuration name (becomes scheme name)
    ConfigurationName:
      # Xcode build settings
      SETTING_NAME: value
```

#### Complete Example

```yaml
---
# E-commerce app example
ShoppingApp:
  name: My Shopping App
  version: 2.1.0
  configs:
    # Debug configuration for development
    Alpha (Debug):
      DEVELOPMENT_TEAM: ABC123DEF4
      PRODUCT_BUNDLE_IDENTIFIER: com.mycompany.shopping.dev
      ASSETCATALOG_COMPILER_APPICON_NAME: appicon_shopping_dev
      # Optional: Alternative app icons
      ASSETCATALOG_COMPILER_ALTERNATE_APPICON_NAMES:
        - appicon_shopping_dev_alt1
        - appicon_shopping_dev_alt2

    # Internal testing configuration
    Alpha:
      DEVELOPMENT_TEAM: ABC123DEF4
      PRODUCT_BUNDLE_IDENTIFIER: com.mycompany.shopping.dev
      NOTIFICATION_SERVICE_BUNDLE_IDENTIFIER: com.mycompany.shopping.dev.NotificationService
      CODE_SIGN_IDENTITY: 'iPhone Distribution: My Company Inc.'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.mycompany.shopping.dev
      ASSETCATALOG_COMPILER_APPICON_NAME: appicon_shopping_dev

    # Beta testing configuration
    Beta:
      DEVELOPMENT_TEAM: ABC123DEF4
      PRODUCT_BUNDLE_IDENTIFIER: com.mycompany.shopping.beta
      NOTIFICATION_SERVICE_BUNDLE_IDENTIFIER: com.mycompany.shopping.beta.NotificationService
      CODE_SIGN_IDENTITY: 'iPhone Distribution: My Company Inc.'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.mycompany.shopping.beta
      ASSETCATALOG_COMPILER_APPICON_NAME: appicon_shopping_beta
      # Firebase configuration for analytics
      FIREBASE_QA_ID: shopping_beta

    # Production release configuration
    Release:
      DEVELOPMENT_TEAM: XYZ789GHI0
      PRODUCT_BUNDLE_IDENTIFIER: com.mycompany.shopping
      NOTIFICATION_SERVICE_BUNDLE_IDENTIFIER: com.mycompany.shopping.NotificationService
      CODE_SIGN_IDENTITY: 'Apple Distribution: My Company Inc.'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore com.mycompany.shopping
      ASSETCATALOG_COMPILER_APPICON_NAME: appicon_shopping_release
      # App Store Connect configuration
      APP_ID: *********0
      TEAM_ID: *********
      SHORT_NAME: XYZ789GHI0

# Second app in the same project
AdminApp:
  name: Admin Dashboard
  version: 1.0.0
  configs:
    Alpha (Debug):
      DEVELOPMENT_TEAM: ABC123DEF4
      PRODUCT_BUNDLE_IDENTIFIER: com.mycompany.admin.dev
    Beta:
      DEVELOPMENT_TEAM: ABC123DEF4
      PRODUCT_BUNDLE_IDENTIFIER: com.mycompany.admin.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: My Company Inc.'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.mycompany.admin.beta
```

#### Common Build Settings

| Setting | Description | Example |
|---------|-------------|---------|
| `DEVELOPMENT_TEAM` | Apple Developer Team ID | `ABC123DEF4` |
| `PRODUCT_BUNDLE_IDENTIFIER` | App bundle identifier | `com.company.app.env` |
| `NOTIFICATION_SERVICE_BUNDLE_IDENTIFIER` | Push notification extension bundle ID | `com.company.app.env.NotificationService` |
| `CODE_SIGN_IDENTITY` | Code signing certificate | `iPhone Distribution: Company Name` |
| `PROVISIONING_PROFILE_SPECIFIER` | Provisioning profile name(s) | `match InHouse com.company.app` |
| `ASSETCATALOG_COMPILER_APPICON_NAME` | App icon set name | `appicon_app_env` |
| `ASSETCATALOG_COMPILER_ALTERNATE_APPICON_NAMES` | Alternative app icons | `[icon1, icon2]` |
| `FIREBASE_QA_ID` | Firebase project identifier | `project_env` |
| `APP_ID` | App Store Connect app ID | `*********0` |
| `TEAM_ID` | App Store Connect team ID | `*********` |

### Dependencies.yml File

The `dependencies.yml` file defines external package dependencies and build tool plugins that your project uses.

#### Structure

```yaml
dependencies:
  # External Swift package dependencies
  - package: PackageName        # Package identifier
    product: ProductName        # Specific product from package (optional)

  # System framework dependencies
  - sdk: SystemFramework.framework

  # Local target dependencies
  - target: LocalTargetName

# Build tool plugins (optional)
buildToolPlugins:
  - package: PackageName
    plugin: PluginName
```

#### Example

```yaml
---
dependencies:
  # External Swift packages
  - package: Alamofire
    product: Alamofire
  - package: SwiftUI-Introspect
    product: Introspect
  - package: Firebase
    product: FirebaseAnalytics
  - package: Firebase
    product: FirebaseCrashlytics

  # System frameworks
  - sdk: UIKit.framework
  - sdk: Foundation.framework
  - sdk: UserNotifications.framework

  # Local packages (auto-discovered from packages-path)
  - package: MyLocalPackage

buildToolPlugins:
  - package: SwiftLint
    plugin: SwiftLintPlugin
  - package: SwiftGen
    plugin: SwiftGenPlugin
```

### Packages.yml File

The `packages.yml` file defines local Swift package configurations and their paths.

#### Structure

```yaml
packages:
  PackageName:
    path: relative/path/to/package    # Path from project root
    # Additional package configuration can go here
```

#### Example

```yaml
---
packages:
  # Local packages in your project
  MyUIComponents:
    path: Packages/MyUIComponents

  NetworkLayer:
    path: Packages/NetworkLayer

  SharedUtilities:
    path: Packages/SharedUtilities

  # Packages in different directories
  DesignSystem:
    path: Libraries/DesignSystem

  # External packages are handled automatically
  # and don't need to be listed here unless you want
  # to override their configuration
```

> **Note**: The tool automatically discovers local packages in directories specified by `--packages-path`, so you typically don't need to manually maintain this file unless you have custom package configurations.

## Build Scripts

The tool supports various build scripts that run during different phases of the build process to automate common tasks.

### Pre-Build Scripts

Pre-build scripts execute before compilation begins. They're useful for code generation, asset processing, and environment setup.

#### Available Pre-Build Scripts

| Script | Description | When to Use |
|--------|-------------|-------------|
| `generateSecretsKeychain` | Generates encrypted secrets from macOS Keychain | When using Keychain-based secrets management |
| `generateSecretsKeystore` | Generates encrypted secrets from keystore file | When using file-based secrets management (default) |
| `generateUICatalog` | Generates UI component catalog using Sourcery | When using automated UI documentation |

#### Usage Example

```bash
# Use custom pre-build scripts
og-deployment-generator \
  --pre-build-scripts generateSecretsKeystore,generateUICatalog \
  --secrets-generation-type keystore
```

### Post-Build Scripts

Post-build scripts execute after compilation completes. They handle versioning, asset copying, and deployment preparation.

#### Available Post-Build Scripts

| Script | Description | When to Use |
|--------|-------------|-------------|
| `updateBuildNumber` | Automatically increments build number based on git commits | For automated versioning in CI/CD |
| `copyGoogleServiceInfoPlist` | Copies appropriate Firebase config for current build configuration | When using Firebase services |
| `uploadSymbolsForCrashlytics` | Uploads dSYM files to Firebase Crashlytics | For crash reporting and debugging |

#### Usage Example

```bash
# Use custom post-build scripts
og-deployment-generator \
  --post-build-scripts updateBuildNumber,copyGoogleServiceInfoPlist
```

### Secrets Generation

The tool supports two methods for managing app secrets and sensitive configuration:

#### Keychain Method (`--secrets-generation-type keychain`)

- **Storage**: Secrets stored in macOS Keychain
- **Security**: High security, encrypted by macOS
- **Access**: Requires Keychain access during build
- **Best for**: Local development, secure environments

```bash
og-deployment-generator --secrets-generation-type keychain
```

#### Keystore Method (`--secrets-generation-type keystore`) - Default

- **Storage**: Secrets stored in encrypted `.ogkeystore` files
- **Security**: Encrypted with custom cipher
- **Access**: Portable, works in CI/CD environments
- **Best for**: Automated builds, team sharing

```bash
og-deployment-generator --secrets-generation-type keystore
```

#### Setting Up Secrets

1. **Create environment variables file** (`.env-vars`):
```bash
API_KEY=your-api-key-here
DATABASE_URL=your-database-url
ANALYTICS_TOKEN=your-analytics-token
```

2. **Generate keystore** (for keystore method):
```bash
./.bin/secret-service generate-keystore --env-path .env-vars --output MyApp/Secrets.ogkeystore
```

3. **Access in code**:
```swift
// Generated code will be available as:
print(Secrets.API_KEY)
print(Secrets.DATABASE_URL)
```

## Custom Templates

You can customize the generated files by providing your own Stencil templates.

### Required Template Files

When using `--templates-dir`, provide these template files:

| File | Purpose | Generates |
|------|---------|-----------|
| `Appfile.rb.stencil` | Fastlane app configuration | `fastlane/Appfile` |
| `Fastfile.rb.stencil` | Fastlane build automation | `fastlane/Fastfile` |
| `main.yml.stencil` | GitHub Actions workflow | `.github/workflows/main.yml` |

### Template Variables

Your templates have access to these variables:

| Variable | Type | Description |
|----------|------|-------------|
| `targets` | Dictionary | All app targets from Apps.yml |
| `projectName` | String | Project name |
| `appIconSetPath` | String | Path to app icon assets |
| `appsDir` | String | Apps directory name |
| `useFramework` | Boolean | Whether using framework architecture |
| `hasPackagesTestHosts` | Boolean | Whether local packages exist |
| `xcodeVersion` | String | Target Xcode version |

### Example Custom Template

```stencil
# Custom Fastfile.rb.stencil
default_platform(:ios)

platform :ios do
  desc "Build {{ projectName }}"
  lane :build do
    {% for targetID in targets %}
    build_app(
      scheme: "{{ targetID }} Beta",
      export_method: "enterprise"
    )
    {% endfor %}
  end
end
```

### Using Custom Templates

```bash
og-deployment-generator \
  --templates-dir MyCustomTemplates \
  --project-name MyApp
```

## Examples

### Example 1: Basic Single-App Project

**Scenario**: Creating a simple iOS app with standard CI/CD setup.

**Apps.yml**:
```yaml
---
MyApp:
  name: My Awesome App
  version: 1.0.0
  configs:
    Alpha (Debug):
      DEVELOPMENT_TEAM: ABC123DEF4
      PRODUCT_BUNDLE_IDENTIFIER: com.mycompany.myapp.dev
    Beta:
      DEVELOPMENT_TEAM: ABC123DEF4
      PRODUCT_BUNDLE_IDENTIFIER: com.mycompany.myapp.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: My Company'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.mycompany.myapp.beta
    Release:
      DEVELOPMENT_TEAM: XYZ789GHI0
      PRODUCT_BUNDLE_IDENTIFIER: com.mycompany.myapp
      CODE_SIGN_IDENTITY: 'Apple Distribution: My Company'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore com.mycompany.myapp
      APP_ID: *********0
```

**Command**:
```bash
og-deployment-generator \
  --project-path /Users/<USER>/MyAwesomeApp \
  --project-name MyAwesomeApp \
  --generate-swift-app-file
```

### Example 2: Multi-App Project with Custom Structure

**Scenario**: Large project with multiple apps, custom directory structure, and shared framework.

**Apps.yml**:
```yaml
---
CustomerApp:
  name: Customer Portal
  version: 2.1.0
  configs:
    Alpha (Debug):
      DEVELOPMENT_TEAM: ABC123DEF4
      PRODUCT_BUNDLE_IDENTIFIER: com.company.customer.dev
    Beta:
      DEVELOPMENT_TEAM: ABC123DEF4
      PRODUCT_BUNDLE_IDENTIFIER: com.company.customer.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Company Inc.'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.company.customer.beta

AdminApp:
  name: Admin Dashboard
  version: 1.5.0
  configs:
    Alpha (Debug):
      DEVELOPMENT_TEAM: ABC123DEF4
      PRODUCT_BUNDLE_IDENTIFIER: com.company.admin.dev
    Beta:
      DEVELOPMENT_TEAM: ABC123DEF4
      PRODUCT_BUNDLE_IDENTIFIER: com.company.admin.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Company Inc.'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.company.admin.beta
```

**Command**:
```bash
og-deployment-generator \
  --project-path /Users/<USER>/CompanyApps \
  --project-name CompanyApps \
  --apps-yml-path config/apps.yml \
  --apps-dir Applications \
  --packages-path Libraries \
  --packages-path SharedComponents \
  --use-framework \
  --shared-framework-name CompanyCore \
  --fastlane-dir deployment/fastlane \
  --github-workflows-dir .github/workflows
```

### Example 3: CI/CD Optimized Setup

**Scenario**: Project optimized for automated builds and deployment.

**Command**:
```bash
og-deployment-generator \
  --project-path $GITHUB_WORKSPACE \
  --project-name MyApp \
  --do-not-open-project \
  --keep-generated-yml \
  --secrets-generation-type keystore \
  --pre-build-scripts generateSecretsKeystore \
  --post-build-scripts updateBuildNumber,copyGoogleServiceInfoPlist,uploadSymbolsForCrashlytics
```

### Example 4: Development-Focused Setup

**Scenario**: Local development with debugging capabilities and custom templates.

**Command**:
```bash
og-deployment-generator \
  --project-path /Users/<USER>/MyProject \
  --project-name MyProject \
  --templates-dir CustomTemplates \
  --secrets-generation-type keychain \
  --keep-generated-yml \
  --generate-swift-app-file \
  --initial-files-path Templates/DefaultAppFiles
```

### Example 5: Single Target Build

**Scenario**: Working on a specific app in a multi-app project.

**Command**:
```bash
og-deployment-generator \
  --project-path /Users/<USER>/MultiApp \
  --project-name MultiApp \
  --target CustomerApp \
  --do-not-open-project
```

### Example 6: Custom Package Structure

**Scenario**: Project with packages in non-standard locations.

**Directory Structure**:
```
MyProject/
├── Apps.yml
├── Applications/
│   ├── MainApp/
│   └── AdminApp/
├── Core/
│   ├── NetworkLayer/
│   └── DataLayer/
├── Features/
│   ├── Authentication/
│   └── UserProfile/
└── UI/
    ├── DesignSystem/
    └── Components/
```

**Command**:
```bash
og-deployment-generator \
  --project-path /Users/<USER>/MyProject \
  --project-name MyProject \
  --apps-dir Applications \
  --packages-path Core \
  --packages-path Features \
  --packages-path UI \
  --dependencies-yml config/dependencies.yml \
  --packages-yml config/packages.yml
```

## Build and Installation

### Prerequisites

- Xcode 15.0 or later
- Swift 5.9 or later
- macOS 12.0 or later

### Building from Source

1. **Clone the repository**:
```bash
git clone <repository-url>
cd Tools/OGDeploymentGenerator
```

2. **Build the tool**:
```bash
swift build --configuration release
```

3. **Install locally**:
```bash
# Copy to local bin directory
cp .build/release/og-deployment-generator /usr/local/bin/

# Or create a symlink
ln -s $(pwd)/.build/release/og-deployment-generator /usr/local/bin/og-deployment-generator
```

### Release Build

To create a release version and install it in your project's bin folder:

```bash
cd Tools/OGDeploymentGenerator
swift build --configuration release --arch arm64 --arch x86_64
cp .build/release/og-deployment-generator ../../bin/OGDeploymentGenerator/og-deployment-generator
```

### Using with Make

If your project has a Makefile with OGDeploymentGenerator build targets:

```bash
# Build and update the tool
make build_og-deployment-generator

# Use the updated tool
bin/OGDeploymentGenerator/og-deployment-generator --help
```

## Troubleshooting

### Common Issues

#### "No templates path" Error
**Problem**: The tool can't find template files.
**Solution**:
- Ensure you're running from the correct directory
- If using `--templates-dir`, verify the path exists and contains required `.stencil` files
- Check that built-in templates are included in the binary

#### "Apps.yml not found" Error
**Problem**: The configuration file can't be located.
**Solution**:
- Verify the file exists at the specified path
- Check the `--apps-yml-path` parameter
- Ensure the file has proper YAML syntax

#### Build Failures
**Problem**: Generated project fails to build.
**Solution**:
- Verify all dependencies in `dependencies.yml` are available
- Check that provisioning profiles and certificates are properly configured
- Ensure Firebase configuration files exist for all environments
- Validate bundle identifiers match provisioning profiles

#### XcodeGen Errors
**Problem**: XcodeGen fails to generate the project.
**Solution**:
- Use `--keep-generated-yml` to inspect intermediate files
- Verify XcodeGen version meets minimum requirements
- Check for syntax errors in generated YAML files

### Debug Mode

Enable debug output by keeping generated files:

```bash
og-deployment-generator \
  --keep-generated-yml \
  --project-path /path/to/project

# Inspect generated files
cat project.yml
cat base.yml
cat targets.yml
```

### Getting Help

1. **View all options**:
```bash
og-deployment-generator --help
```

2. **Check version compatibility**:
```bash
xcodegen --version  # Should be >= minimum required version
```

3. **Validate configuration files**:
```bash
# Check YAML syntax
python -c "import yaml; yaml.safe_load(open('Apps.yml'))"
```
