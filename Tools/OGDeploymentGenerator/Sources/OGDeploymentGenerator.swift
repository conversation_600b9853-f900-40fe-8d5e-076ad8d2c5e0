import ArgumentParser
import Foundation
import PathKit
import Yams

// MARK: - OGDeploymentGenerator

@main
struct OGDeploymentGenerator: ParsableCommand {
  @Option(help: "Absolute path to the root directory of your iOS project. This is where the tool will generate all project files and look for configuration files. Default: current working directory.") private var projectPath: String = "/Users/<USER>/og-dx_aac-ios-blossom" //FileManager.default.currentDirectoryPath
  
  @Option(help: "Relative path from project root to the Apps.yml configuration file that defines your app targets and their build configurations. Example: 'config/MyApps.yml'. Default: 'Apps.yml'.") private var appsYmlPath: String = "OGAppKitApps.yml"

  @Option(name: .long, help: "Relative path from project root to a custom templates directory containing Stencil template files (Appfile.rb.stencil, Fastfile.rb.stencil, main.yml.stencil). If not specified, built-in default templates are used. Example: 'CustomTemplates'.") private var templatesDir: String?

  @Option(name: .long, help: "Relative path from project root to the directory where GitHub Actions workflow files will be generated. The tool creates CI/CD workflows for building and deploying your apps. Default: '.github/workflows'.") private var githubWorkflowsDir: String = ".github/workflows"

  @Option(name: .long, help: "Relative path from project root to the directory where Fastlane configuration files (Appfile, Fastfile) will be generated. Fastlane automates iOS app building, testing, and deployment. Default: 'fastlane'.") private var fastlaneDir: String = "fastlane"

  @Option(name: .long, help: "Relative path from project root to the directory containing your individual app target folders. Each app defined in Apps.yml will have its own subdirectory here. Default: 'Apps'.") private var appsDir: String = "Apps"

  @Option(name: .long, help: "Relative path from project root to the directory containing Firebase configuration files (GoogleService-Info.plist) organized by environment (alpha/beta/release). Default: 'Firebase'.") private var firebaseInfoPlistDir: String = "Firebase"

  @Option(name: .long, help: "The name of your Xcode project. This will be used as the .xcodeproj filename and in various configuration files. Example: 'MyAwesomeApp'. Default: 'OttoGroup'.") private var projectName: String = "OttoGroup"

  @Option(name: .long, help: "Relative path from project root to the .appiconset directory containing your app icon assets. Used for configuring app icon references in the generated project. Default: 'Resources/Assets.xcassets/AppIcon.appiconset'.") private var appIconSetPath: String = "Resources/Assets.xcassets/AppIcon.appiconset"

  @Option(name: .long, help: "Array of relative paths from project root to directories containing local Swift packages. These packages will be automatically discovered and added as dependencies. Specify multiple paths by repeating the option: --packages-path Packages --packages-path OGKit --packages-path Libraries. Default: ['Packages', 'OGKit'].") private var packagesPath: [String] = ["Packages", "OGKit"]

  @Option(name: .long, help: "Xcode version to target for the generated project and CI/CD workflows. This affects build settings and GitHub Actions runner configuration. Format: 'X.Y' (e.g., '16.0'). Default: '16.0'.") private var xcodeVersion: String = "16.0"

  @Option(name: .long, help: "Minimum iOS deployment target version for your apps. This sets the IPHONEOS_DEPLOYMENT_TARGET build setting. Format: 'X.Y' (e.g., '15.0'). Default: '15.0'.") private var iosVersion: String = "15.0"

  @Option(name: .long, help: "Swift language version to use in the generated project. This sets the SWIFT_VERSION build setting. Format: 'X.Y' (e.g., '5.9'). Default: '5.9'.") private var swiftVersion: String = "5.9"

  @Option(name: .long, help: "Minimum required version of XcodeGen tool for generating the Xcode project. Ensures compatibility with project generation features. Format: 'X.Y.Z' (e.g., '2.44.1'). Default: '2.44.1'.") private var minimumXcodeGenVersion: String = "2.44.1"

  @Option(name: .long, help: "Method for generating and storing app secrets during build. 'keychain' uses macOS Keychain, 'keystore' uses encrypted file storage. Affects pre-build script selection. Options: keychain, keystore. Default: keystore.") private var secretsGenerationType: SecretsGenerationType = .keystore

  @Option(name: .long, help: "Relative path from project root to a directory containing template files that should be copied to each new app target during initialization. Useful for providing default source files, resources, or configuration. Example: 'Templates/AppTemplate'.") private var initialFilesPath: String?

  @Option(name: .long, help: "Array of pre-build scripts to execute before compiling each app target. These run during Xcode's build phases. Available scripts: generateSecretsKeychain, generateSecretsKeystore, generateUICatalog. Default: [generateSecretsKeychain, generateUICatalog].") private var preBuildScripts: [ScriptEnum] = [
    .generateSecretsKeychain,
    .generateUICatalog
  ]

  @Option(name: .long, help: "Array of post-build scripts to execute after compiling each app target. These run during Xcode's build phases for tasks like versioning and deployment preparation. Available scripts: updateBuildNumber, copyGoogleServiceInfoPlist, uploadSymbolsForCrashlytics. Default: [updateBuildNumber, copyGoogleServiceInfoPlist, uploadSymbolsForCrashlytics].") private var postBuildScripts: [ScriptEnum] = [
    .updateBuildNumber,
    .copyGoogleServiceInfoPlist,
    .uploadSymbolsForCrashlytics
  ]
  @Option(name: .long, help: "Relative path from project root to the dependencies.yml file that defines external package dependencies and build tool plugins for your project. This file specifies Swift packages, SDKs, and other dependencies. Default: 'dependencies.yml'.") private var dependenciesYml: String = DefaultName.File.dependenciesYML

  @Option(name: .long, help: "Relative path from project root to the packages.yml file that defines local package configurations. This file maps local Swift packages to their paths and settings. Default: 'packages.yml'.") private var packagesYml: String = DefaultName.File.packagesYML

  @Option(name: .long, help: "Custom name for the shared framework target when using --use-framework. This framework contains shared code used by all app targets. Only relevant when --use-framework is enabled. Default: 'OGShared'.") private var sharedFrameworkName: String?

  @Option(name: .long, help: "Build only a specific app target instead of all targets defined in Apps.yml. Useful for focused development or testing. Specify the target key from your Apps.yml file (e.g., 'WIT', 'ACME'). If not specified, all targets are built.") private var target: String?

  @Flag(name: .long, help: "Use a shared framework architecture instead of a shared source folder. When enabled, creates a framework target containing shared code that all apps link against. When disabled, uses a shared source directory that apps compile directly.") private var useFramework: Bool = false

  @Flag(name: .long, help: "Preserve intermediate YAML files generated during project creation (project.yml, base.yml, targets.yml). Useful for debugging project generation issues or understanding the XcodeGen configuration. Default: files are deleted after use.") private var keepGeneratedYML: Bool = false

  @Flag(name: .long, help: "Skip automatically opening the generated Xcode project after creation. By default, the tool opens the .xcodeproj file in Xcode when generation completes. Use this flag for automated scripts or CI environments.") private var doNotOpenProject: Bool = false

  @Flag(name: .long, help: "Generate a basic Swift App struct file (MyAppView.swift) for new app targets when --initial-files-path is not specified. Provides a minimal starting point for SwiftUI apps. Only applies to newly created app targets.") private var generateSwiftAppFile: Bool = false

  public mutating func run() throws {
    if let sharedFrameworkName {
      DefaultNames.shared.frameworkName = sharedFrameworkName
    }
    // Use the first package path as the default folder for backward compatibility
    DefaultNames.shared.packagesFolder = packagesPath.first ?? "Packages"
    DefaultNames.shared.appsFolder = appsDir

    FileGenerator.shared.projectPath = projectPath
    try copyBinFiles()

    let extractedPackages = try extractPackageData()
    let updatedPackages = try mergePackageData(with: extractedPackages)
    let updatedDependencies = try mergeDependenciesData(with: updatedPackages, and: extractedPackages)
    let loadedBuildToolPlugins = try buildToolPluginsData()
    let targets: Targets
    if let target {
      targets = try loadAppsData().filter { $0.key == target }
    } else {
      targets = try loadAppsData()
    }
    try copyAppsTargetsFiles(with: targets)
    let appsTargets = buildAppsTargets(with: targets, and: updatedDependencies, and: loadedBuildToolPlugins)
    let appsTargetsSchemes = buildAppsTargetsSchemes(with: targets)

    try buildBaseYML()
    try buildTargetsYML(with: appsTargets, and: appsTargetsSchemes)
    try buildPackagesYML(with: updatedPackages)
    try buildDependenciesYML(with: updatedDependencies, and: loadedBuildToolPlugins)
    try buildProjectYML(with: updatedDependencies, and: loadedBuildToolPlugins)

    runXcodeGen()
    if !keepGeneratedYML {
     // try deleteGeneratedYMLs()
    }
    if !doNotOpenProject {
      shell("open \(projectName).xcodeproj")
    }
    try generateDeploymentData(with: targets)
  }

  private func copyBinFiles() throws {
    try FileGenerator.shared.copyToBinFolder()
  }

  private mutating func handleSecretsGenerationType() {
    switch secretsGenerationType {
    case .keychain:
      if let index = preBuildScripts.firstIndex(of: .generateSecretsKeystore) {
        preBuildScripts[index] = .generateSecretsKeychain
      }
    case .keystore:
      if let index = preBuildScripts.firstIndex(of: .generateSecretsKeychain) {
        preBuildScripts[index] = .generateSecretsKeystore
      }
    }
  }

  private mutating func buildBaseYML() throws {
    handleSecretsGenerationType()

    let application = DefaultApplication(
      preBuildScripts: preBuildScripts.map(\.preScript),
      postBuildScripts: postBuildScripts.map(\.postScript),
      useFramework: useFramework
    ).build()
    let test = DefaultTest().build()
    let notificationService = DefaultAirshipNotificationService(postBuildScripts: [PostBuildScript.updateBuildNumber]).build()
    let framework = useFramework ? DefaultOGShared().build() : nil

    let swiftLintable = SwiftLintable(postCompileScripts: [.lint])

    let templates = Templates(
      application: application,
      tests: test,
      notificationService: notificationService,
      framework: framework,
      swiftLintable: swiftLintable
    )

    let encoder = YAMLEncoder()
    encoder.options = YAMLEncoder.Options(sortKeys: true)
    let targetTemplates = try encoder.encode(TargetTemplates(targetTemplates: templates))
    try targetTemplates.save(to: FileGenerator.shared.prependProjectPath(for: DefaultName.File.baseYML))
  }

  private func loadAppsData() throws -> Targets {
    let yamlDecoder = YAMLDecoder()
    let data = try String(contentsOfFile: FileGenerator.shared.prependProjectPath(for: appsYmlPath), encoding: .utf8)
    return try yamlDecoder.decode(Targets.self, from: data)
  }

  private func copyAppsTargetsFiles(with targets: Targets) throws {
    try targets.forEach {
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Config")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/alpha")
      try FileGenerator.shared.copyFile(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/GoogleService-Info-alpha.plist", toPath: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/alpha/GoogleService-Info.plist")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/beta")
      try FileGenerator.shared.copyFile(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/GoogleService-Info-beta.plist", toPath: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/beta/GoogleService-Info.plist")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/release")
      try FileGenerator.shared.copyFile(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/GoogleService-Info-release.plist", toPath: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/release/GoogleService-Info.plist")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Resources")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Sources")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)/Info.plist", with: "defaultFiles/app/Info.plist")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)/app.entitlements", with: "defaultFiles/app/app.entitlements")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)/.assetsFetcher.yml", with: "defaultFiles/app/assetsFetcher.yml")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)/.phraseapp.yml", with: "defaultFiles/app/phraseapp.yml")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)/Sources/CodeGenerated/Secrets.generated.swift")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)-Tests/Sources")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)-Tests/Sources/\($0.key)-Tests.swift", with: "defaultFiles/tests/Tests.swift")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)-Tests/Info.plist")
      if let initialFilesPath {
        try FileGenerator.shared.copyInitialFiles(at: "\(DefaultName.Folder.apps)/\($0.key)", contentOfPath: initialFilesPath)
      } else if generateSwiftAppFile {
        try FileGenerator.shared.createInitialSwiftFileIfNeeded(at: "\(DefaultName.Folder.apps)/\($0.key)/Sources/", fileName: "MyAppView.swift", contentOfPath: "defaultFiles/app/MyAppView.swift")
      }
    }
    if !useFramework {
      try FileGenerator.shared.createDir(at: DefaultName.buildPath(names: [.Folder.shared, .Folder.sources]))
    }
  }

  private func buildAppsTargets(with targets: Targets, and projectDependencies: [Project.Target.Dependency], and projectBuildToolPlugins: [Project.Target.BuildToolPlugin]?) -> [String: AppTarget] {
    var dependencies = [AppTarget.Dependency]()
    var buildToolPlugins = [AppTarget.BuildToolPlugin]()
    if useFramework {
      dependencies = [AppTarget.Dependency(target: DefaultName.Target.ogShared)]
    } else {
      dependencies = projectDependencies.map {
        AppTarget.Dependency(sdk: $0.sdk, package: $0.package, product: $0.product, target: $0.target)
      }
      buildToolPlugins = projectBuildToolPlugins?.compactMap {
        AppTarget.BuildToolPlugin(package: $0.package, plugin: $0.plugin)
      } ?? []
    }
    var appTargets = [String: AppTarget]()
    for target in targets {
      appTargets[target.key] = AppTarget(
        templates: ["Application"],
        dependencies: dependencies + [AppTarget.Dependency(target: "\(target.key)-" + DefaultName.Target.notificationService)],
        settings: AppTarget.Settings(
          base: AppTarget.Base(
            displayName: target.value.name,
            marketingVersion: target.value.version,
            executableName: target.key,
            productName: target.key
          ),
          configs: target.value.filterProvisioningProfileSpecifier(for: DefaultName.Target.notificationService).configs
        ),
        buildToolPlugins: buildToolPlugins
      )
      appTargets[target.key + "-Tests"] = AppTarget(
        templates: ["Tests"],
        settings: AppTarget.Settings(
          base: AppTarget.Base(testHost: "$(BUILT_PRODUCTS_DIR)/\(target.key).app/\(target.key)")),
        templateAttributes: AppTarget.TemplateAttribute(targetID: target.key))
      let configs = target.value.provisioningProfileSpecifier(for: DefaultName.Target.notificationService).configs.mapValues {
        ConfigProperties(
          developmentTeam: $0.developmentTeam,
          productBundleIdentifier: $0.notificationServiceBundleIdentifier ?? $0.productBundleIdentifier + "." + DefaultName.Target.notificationService,
          codeSignIdentity: $0.codeSignIdentity,
          provisioningProfileSpecifier: $0.provisioningProfileSpecifier
        )
      }

      appTargets[target.key + "-" + DefaultName.Target.notificationService] = AppTarget(
        templates: [DefaultName.Target.notificationService],
        settings: AppTarget.Settings(
          base: AppTarget.Base(marketingVersion: target.value.version),
          configs: configs
        ))
    }
    return appTargets
  }

  private func buildAppsTargetsSchemes(with targets: Targets) -> [String: AppTarget.Scheme] {
    var schemes = [String: AppTarget.Scheme]()
    for target in targets.sorted(by: { $0.key < $1.key }) {
      for config in Configuration.allCases.sorted(by: ({ $0.rawValue < $1.rawValue })) {
        let fbDebugDisabled = config != .beta
        let fbDebugEnabled = config == .beta
        schemes["\(target.key) \(config.rawValue)"] = AppTarget.Scheme(
          build: AppTarget.Build(targets: [
            target.key: [.run, .profile, .analyze, .archive],
            target.key + "-Tests": [.test]
          ]),
          run: AppTarget.Run(
            config: config,
            commandLineArguments:
            AppTarget.Run.CommandLineArgument(
              fbDebugEnabled: fbDebugEnabled,
              fbDebugDisabled: fbDebugDisabled
            )
          ),
          test: AppTarget.Test(
            targets: [target.key + "-Tests"],
            config: config
          ),
          profile: AppTarget.Profile(config: config),
          analyze: AppTarget.Analyze(config: config),
          archive: AppTarget.Archive(config: config))
      }
    }
    return schemes
  }

  private func buildTargetsYML(with appTargets: [String: AppTarget], and schemes: [String: AppTarget.Scheme]) throws {
    let xcodeProject = AppTarget.XcodeProject(
      targets: appTargets,
      schemes: schemes
    )
    let encoder = YAMLEncoder()
    encoder.options = YAMLEncoder.Options(sortKeys: true)
    let data = try encoder.encode(xcodeProject)
    try data.save(to: FileGenerator.shared.prependProjectPath(for: DefaultName.File.targetsYML))
  }

  private func generateDeploymentData(with targets: Targets) throws {
    let model = OGDeploymentGeneratorModel(
      projectPath: projectPath,
      githubWorkflowsDir: githubWorkflowsDir,
      fastlaneDir: fastlaneDir,
      appsDir: DefaultName.Folder.apps,
      firebaseInfoPlistDir: firebaseInfoPlistDir,
      targets: targets
    )

    let templatesDirectory = (templatesDir.map { projectPath.withTrailingSlash() + $0 } ?? Bundle.module.resourcePath?.appendPathComponent("Templates"))
    guard let finalDirectory = templatesDirectory else {
      throw OGDeploymentGeneratorError.noTemplatesPath
    }

    let stencilHelper = StencilHelper(templatesDir: finalDirectory)

    try model.makeGenerationData().forEach {
      try stencilHelper.write(
        data: $0,
        projectName: projectName,
        appIconSetPath: appIconSetPath,
        appsDir: DefaultName.Folder.apps,
        useFramework: useFramework,
        hasPackagesTestHosts: !extractPackageData().isEmpty,
        xcodeVersion: xcodeVersion
      )
    }
  }

  private func extractPackageData() throws -> [String: String] {
    var allPackageFolders: [String] = []

    // Iterate through all package paths and collect package folders from each
    for packagePath in packagesPath {
      let packageFolders = try findPackages(at: FileGenerator.shared.prependProjectPath(for: packagePath))
      allPackageFolders.append(contentsOf: packageFolders)
    }

    return try extractPackages(at: allPackageFolders)
  }

  private func mergePackageData(with extractedPackages: [String: String]) throws -> [String: Package] {
    let yamlDecoder = YAMLDecoder()
    let packagesData = try String(contentsOfFile: FileGenerator.shared.prependProjectPath(for: packagesYml), encoding: .utf8)
    let currentPackages = try yamlDecoder.decode(Packages.self, from: packagesData).packages.compactMapValues {
      $0.path == nil ? $0 : nil
    }

    let packages = extractedPackages.compactMapValues {
      Package(path: $0)
    }

    return currentPackages.merging(packages) { _, new in new }
  }

  private func buildPackagesYML(with packages: [String: Package]) throws {
    let encoder = YAMLEncoder()
    encoder.options = YAMLEncoder.Options(sortKeys: true)
    let yamlPackaget = try encoder.encode(Packages(packages: packages))
    try yamlPackaget.save(to: FileGenerator.shared.prependProjectPath(for: packagesYml))
  }

  private func mergeDependenciesData(with packages: [String: Package], and extractedPackages: [String: String]) throws -> [Project.Target.Dependency] {
    let dependenciesData = try String(contentsOfFile: FileGenerator.shared.prependProjectPath(for: dependenciesYml), encoding: .utf8)
    let yamlDecoder = YAMLDecoder()
    let currentDependencies = try yamlDecoder.decode(Dependencies.self, from: dependenciesData).dependencies.compactMap {
      packages[$0.package ?? ""] == nil && $0.package != nil ? nil : $0
    }

    let dependencies = extractedPackages.map {
      Project.Target.Dependency(package: $0.key)
    }
    return Array(Set(currentDependencies).union(Set(dependencies))).sorted { lhs, rhs in
      if let lhsPackage = lhs.package, let rhsPackage = rhs.package, let lhsProduct = lhs.product, let rhsProduct = rhs.product {
        return lhsPackage + lhsProduct < rhsPackage + rhsProduct
      }
      return lhs.package ?? lhs.sdk ?? lhs.target ?? "" < rhs.package ?? rhs.sdk ?? rhs.target ?? ""
    }
  }

  private func buildToolPluginsData() throws -> [Project.Target.BuildToolPlugin]? {
    let dependenciesData = try String(contentsOfFile: FileGenerator.shared.prependProjectPath(for: dependenciesYml), encoding: .utf8)
    let yamlDecoder = YAMLDecoder()
    return try yamlDecoder.decode(Dependencies.self, from: dependenciesData).buildToolPlugins
  }

  private func buildDependenciesYML(with updatedDependencies: [Project.Target.Dependency], and updateBuildToolPlugins: [Project.Target.BuildToolPlugin]?) throws {
    let encoder = YAMLEncoder()
    encoder.options = YAMLEncoder.Options(sortKeys: true)
    let yamlDependencies = try encoder.encode(Dependencies(dependencies: updatedDependencies, buildToolPlugins: updateBuildToolPlugins))
    try yamlDependencies.save(to: FileGenerator.shared.prependProjectPath(for: dependenciesYml))
  }

  private func buildProjectYML(with updatedDependencies: [Project.Target.Dependency], and updatedBuildToolPlugins: [Project.Target.BuildToolPlugin]?) throws {
    let encoder = YAMLEncoder()
    encoder.options = YAMLEncoder.Options(sortKeys: true)
    let project = DefaultProject(
      name: projectName,
      swiftVersion: swiftVersion,
      iOSVersion: iosVersion,
      xcodeVersion: xcodeVersion,
      minimumXcodeGenVersion: minimumXcodeGenVersion,
      dependencies: updatedDependencies,
      buildToolPlugins: updatedBuildToolPlugins,
      useFramework: useFramework,
      packagePaths: packagesPath
    ).build()
    let yamlProject = try encoder.encode(project)
    try yamlProject.save(to: FileGenerator.shared.prependProjectPath(for: DefaultName.File.projectYML))
  }

  private func runXcodeGen() {
    shell("./.bin/bin/xcodegen --use-cache")
  }

  private func deleteGeneratedYMLs() throws {
    try FileManager.default.removeItem(atPath: FileGenerator.shared.prependProjectPath(for: DefaultName.File.projectYML))
    try FileManager.default.removeItem(atPath: FileGenerator.shared.prependProjectPath(for: DefaultName.File.baseYML))
    try FileManager.default.removeItem(atPath: FileGenerator.shared.prependProjectPath(for: DefaultName.File.targetsYML))
  }

  @discardableResult
  func shell(_ command: String) -> (String?, Int32) {
    let task = Process()

    task.launchPath = "/bin/bash"
    task.arguments = ["-c", command]

    let pipe = Pipe()
    task.standardOutput = pipe
    task.standardError = pipe
    task.launch()

    let data = pipe.fileHandleForReading.readDataToEndOfFile()
    let output = String(data: data, encoding: .utf8)
    task.waitUntilExit()
    return (output, task.terminationStatus)
  }


  private func findPackages(at path: String, infinitiveDepth: Bool = true) throws -> [String] {
      guard FileManager.default.fileExists(atPath: path) else { return [] }
      let folder = path.components(separatedBy: "/").last ?? ""
      
      if infinitiveDepth {
          return try FileManager.default.subpathsOfDirectory(atPath: path)
              .filter { !$0.components(separatedBy: "/").contains { $0.hasPrefix(".build") } }
              .compactMap { $0.hasSuffix("Package.swift") ? "\(path)/\($0)" : nil }
      } else {
          return try FileManager.default.subpathsOfDirectory(atPath: path)
              .filter { !$0.components(separatedBy: "/").contains { $0.hasPrefix(".build") } }
              .compactMap { $0.hasSuffix("Package.swift") ? "\(path)/\($0)" : nil }
              .compactMap { $0.components(separatedBy: folder).count == 2 ? "\($0)" : nil }
      }
  }

  private func extractPackages(at path: [String]) throws -> [String: String] {
    var libraryNames = [String: String]()
    for packagePath in path {
      guard FileManager.default.fileExists(atPath: packagePath) else { continue }
      var content = try String(contentsOfFile: packagePath, encoding: .utf8)
      content = String(content.compactMap { $0.isNewline || $0.isWhitespace ? nil : $0 })

      let range = NSRange(location: 0, length: content.utf16.count)
      let regexOptions: NSRegularExpression.Options = [.allowCommentsAndWhitespace]
      let libraryRegex = try NSRegularExpression(pattern: ".library\\(.*?\\)", options: regexOptions)
      let libraryResults = libraryRegex.matches(in: content, range: range).map {
        String(content[Range($0.range, in: content)!])
      }

      let libraryNameResults = try libraryResults.compactMap {
        let range = NSRange(location: 0, length: $0.utf16.count)
        let libraryNameRegex = try NSRegularExpression(pattern: "name:(.*?),", options: regexOptions)
        if let libraryNameResult = libraryNameRegex.firstMatch(in: $0, range: range) {
          return String($0[Range(libraryNameResult.range(at: 1), in: $0)!])
        } else {
          return nil
        }
      }
      let relativePackagePath = packagePath
        .replacingOccurrences(of: projectPath, with: "")
        .replacingOccurrences(of: "Package.swift", with: "")
        .dropFirst()

      let cleanedLibraryNames = libraryNameResults.map { $0.replacingOccurrences(of: "\"", with: "") }

      // Completely ignore TestsUtils libraries - only process main libraries
      let mainLibraryNames = cleanedLibraryNames.filter { !$0.hasSuffix("TestsUtils") }

      // Only add the first main library (ignore packages that only have TestsUtils)
      if let libraryName = mainLibraryNames.first {
        libraryNames[libraryName] = String(relativePackagePath)
      }
    }
    return libraryNames
  }
}

// MARK: OGDeploymentGenerator.OGDeploymentGeneratorError

extension OGDeploymentGenerator {
  enum OGDeploymentGeneratorError: Error {
    case noTemplatesPath
  }
}

// MARK: OGDeploymentGenerator.SecretsGenerationType

extension OGDeploymentGenerator {
  enum SecretsGenerationType: String, ExpressibleByArgument {
    init?(argument: String) {
      self.init(rawValue: argument)
    }

    case keychain
    case keystore
  }
}

// MARK: OGDeploymentGenerator.ScriptEnum

extension OGDeploymentGenerator {
  enum ScriptEnum: String, ExpressibleByArgument {
    init?(argument: String) {
      self.init(rawValue: argument)
    }

    case generateSecretsKeychain
    case generateSecretsKeystore
    case updateBuildNumber
    case copyGoogleServiceInfoPlist
    case uploadSymbolsForCrashlytics
    case generateUICatalog

    var postScript: PostBuildScript {
      switch self {
      case .updateBuildNumber:
        return PostBuildScript.updateBuildNumber
      case .copyGoogleServiceInfoPlist:
        return PostBuildScript.copyGoogleServiceInfoPlist
      case .uploadSymbolsForCrashlytics:
        return PostBuildScript.uploadSymbolsForCrashlytics
      default:
        fatalError()
      }
    }

    var preScript: PreBuildScript {
      switch self {
      case .generateSecretsKeychain:
        return PreBuildScript.generateSecretsKeychain
      case .generateSecretsKeystore:
        return PreBuildScript.generateSecretsKeystore
      case .generateUICatalog:
        return PreBuildScript.generateUICatalog
      default:
        fatalError()
      }
    }
  }
}
