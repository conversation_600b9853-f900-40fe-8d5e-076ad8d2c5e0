import Foundation

struct DefaultProject {
  let name: String
  let swiftVersion: String
  let iOSVersion: String
  let xcodeVersion: String
  let minimumXcodeGenVersion: String
  let dependencies: [Project.Target.Dependency]
  let buildToolPlugins: [Project.Target.BuildToolPlugin]?
  let useFramework: Bool
  let packagePaths: [String]
  func build() -> Project {
    let include = [DefaultName.File.baseYML, DefaultName.File.packagesYML, DefaultName.File.targetsYML]
    let attributes = ["ORGANIZATIONNAME": "Otto Group"]
    let buildConfigs = [
      Configuration.debug: Configuration.debug.build,
      Configuration.alpha: Configuration.alpha.build,
      Configuration.beta: Configuration.beta.build,
      Configuration.release: Configuration.release.build
    ]

    let base = [
      "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym",
      "SWIFT_VERSION": swiftVersion,
      "TARGETED_DEVICE_FAMILY": "1,2",
      "VERSIONING_SYSTEM": "apple-generic"
    ]

    let settings = Project.Settings(base: base, configs: [
      .debug: .debugSettings,
      .alpha: .alphaSettings,
      .beta: .bataSettings,
      .release: .releaseSettings
    ])

    let options = Project.Options(
      bundleIdPrefix: "com.ottogroup",
      defaultConfig: .debug,
      deploymentTarget: ["iOS": iOSVersion],
      xcodeVersion: xcodeVersion,
      usesTabs: false,
      indentWidth: 2,
      tabWidth: 2,
      minimumXcodeGenVersion: minimumXcodeGenVersion,
      createIntermediateGroups: true,
      generateEmptyDirectories: true,
      groupSortPosition: "top",
      useBaseInternationalization: false
    )
    let (packageTargets, packageSchemes) = createPackageTargets()
    let targets: [String: Project.Target]
    let schemes: [String: Project.Scheme]

    if useFramework {
      targets = [
        DefaultName.Target.ogShared: Project.Target.ogShared(dependencies: dependencies, buildToolPlugins: buildToolPlugins),
        DefaultName.Target.ogSharedTestHost: Project.Target.ogSharedTestHost,
        DefaultName.Target.ogSharedTests: Project.Target.ogSharedTest
      ].merging(packageTargets) { _, new in new }
      schemes = [
        DefaultName.Target.ogShared: Project.Scheme.ogSharedScheme,
        DefaultName.Target.ogSharedTestHost: Project.Scheme.ogSharedTestHostScheme
      ].merging(packageSchemes) { _, new in new }
    } else {
      targets = packageTargets
      schemes = packageSchemes
    }
    return Project(
      name: name,
      include: include,
      attributes: attributes,
      configs: buildConfigs,
      settings: settings,
      options: options,
      targets: targets,
      schemes: schemes
    )
  }

  private func findPackages(at path: String) throws -> [String] {
    guard FileManager.default.fileExists(atPath: path) else { return [] }
    return try FileManager.default.subpathsOfDirectory(atPath: path)
      .filter { !$0.components(separatedBy: "/").contains { $0.hasPrefix(".build") } }
      .compactMap { $0.hasSuffix("Package.swift") ? "\(path)/\($0)" : nil }
  }

  private func createPackageTargets() -> (targets: [String: Project.Target], schemes: [String: Project.Scheme]) {
    var targets = [String: Project.Target]()
    var schemes = [String: Project.Scheme]()

    // Collect packages from all package paths
    var allPackagePaths: [String] = []
    for packagePath in packagePaths {
      let packagesInPath = (try? findPackages(at: FileGenerator.shared.prependProjectPath(for: packagePath))) ?? []
      allPackagePaths.append(contentsOf: packagesInPath)
    }
    let path = allPackagePaths

    func extractNameFrom(from content: String, for target: String) throws -> String? {
      let range = NSRange(location: 0, length: content.utf16.count)
      let regexOptions: NSRegularExpression.Options = [.allowCommentsAndWhitespace]
      let targetRegex = try NSRegularExpression(pattern: ".\(target)\\(.*?\\)", options: regexOptions)
      let targetResults = targetRegex.matches(in: content, range: range).map {
        String(content[Range($0.range, in: content)!])
      }

      let nameResults = try targetResults.compactMap {
        let range = NSRange(location: 0, length: $0.utf16.count)
        let nameRegex = try NSRegularExpression(pattern: "name:(.*?),", options: regexOptions)
        if let nameResult = nameRegex.firstMatch(in: $0, range: range) {
          return String($0[Range(nameResult.range(at: 1), in: $0)!])
        } else {
          return nil
        }
      }

      let cleanedNames = nameResults.map { $0.trimmingCharacters(in: .whitespacesAndNewlines).trimmingCharacters(in: CharacterSet(["\""]) ) }

      // For regular targets, prefer the main library target (not TestsUtils targets)
      if target == "target" {
        // First, try to find a target that doesn't end with "TestsUtils"
        if let mainTarget = cleanedNames.first(where: { !$0.hasSuffix("TestsUtils") }) {
          return mainTarget
        }
      }

      // For testTarget or if no main target found, return the first one
      return cleanedNames.first
    }
    var packageNames = [Project.Target.Dependency]()
    var packageTestNames = [Project.Target.Dependency]()
    for packagePath in path {
      guard FileManager.default.fileExists(atPath: packagePath) else { continue }
      guard var content = try? String(contentsOfFile: packagePath, encoding: .utf8) else { continue }
      content = String(content.compactMap { $0.isNewline || $0.isWhitespace ? nil : $0 })

      // Only process packages that have .library products (excluding TestsUtils libraries)
      let range = NSRange(location: 0, length: content.utf16.count)
      let regexOptions: NSRegularExpression.Options = [.allowCommentsAndWhitespace]
      guard let libraryRegex = try? NSRegularExpression(pattern: ".library\\(.*?\\)", options: regexOptions) else { continue }
      let libraryResults = libraryRegex.matches(in: content, range: range)

      // Extract library names and filter out TestsUtils libraries
      let libraryNames = try libraryResults.compactMap { match -> String? in
        let matchString = String(content[Range(match.range, in: content)!])
        let nameRange = NSRange(location: 0, length: matchString.utf16.count)
        let nameRegex = try NSRegularExpression(pattern: "name:(.*?),", options: regexOptions)
        if let nameResult = nameRegex.firstMatch(in: matchString, range: nameRange) {
          return String(matchString[Range(nameResult.range(at: 1), in: matchString)!])
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .trimmingCharacters(in: CharacterSet(["\""] ))
        }
        return nil
      }

      // Skip packages that only have TestsUtils libraries
      let mainLibraries = libraryNames.filter { !$0.hasSuffix("TestsUtils") }
      guard !mainLibraries.isEmpty else { continue }

      guard
        let packageName = try? extractNameFrom(from: content, for: "target"),
        let packageTestName = try? extractNameFrom(from: content, for: "testTarget") else { continue }

      packageNames.append(Project.Target.Dependency(package: packageName))
      packageTestNames.append(Project.Target.Dependency(package: "\(packageName)/\(packageTestName)"))
    }

    let testHost = Project.Target(
      type: "application",
      platform: "iOS",
      dependencies: packageNames,
      sources: [
        Project.Target.Source(path: "\(DefaultName.Target.packagesTestHosts)/Sources"),
        Project.Target.Source(path: "\(DefaultName.Target.packagesTestHosts)/Info.plist")
      ]
    )

    try? FileGenerator.shared.createFile(at: "\(DefaultName.Target.packagesTestHosts)/Info.plist", with: "defaultFiles/app/Info.plist")
    try? FileGenerator.shared.createDir(at: "\(DefaultName.Target.packagesTestHosts)/Sources")
    try? FileGenerator.shared.createFile(at: "\(DefaultName.Target.packagesTestHosts)/Sources/AppDelegate.swift", with: "defaultFiles/tests/AppDelegate.swift")

    targets[DefaultName.Target.packagesTestHosts] = testHost

    let schemeBuild = Project.Scheme.Build(targets: [DefaultName.Target.packagesTestHosts: [.testing]])
    let schemeTest = Project.Scheme.Test(config: .debug, targets: packageTestNames)

    let scheme = Project.Scheme(build: schemeBuild, test: schemeTest, profile: Project.Scheme.Profile(config: .release), run: Project.Scheme.Run(config: .debug))

    schemes[DefaultName.Target.packagesTestHosts] = scheme

    return (targets, schemes)
  }
}
