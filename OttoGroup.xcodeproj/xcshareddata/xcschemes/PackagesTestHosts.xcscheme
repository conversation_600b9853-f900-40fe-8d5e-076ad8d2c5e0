<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1600"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES"
      runPostActionsOnFailure = "NO">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "NO"
            buildForProfiling = "NO"
            buildForArchiving = "NO"
            buildForAnalyzing = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "8305ECA24D262C3BB9681D98"
               BuildableName = "PackagesTestHosts.app"
               BlueprintName = "PackagesTestHosts"
               ReferencedContainer = "container:OttoGroup.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Alpha (Debug)"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      onlyGenerateCoverageForSpecifiedTargets = "NO">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "8305ECA24D262C3BB9681D98"
            BuildableName = "PackagesTestHosts.app"
            BlueprintName = "PackagesTestHosts"
            ReferencedContainer = "container:OttoGroup.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <Testables>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "RecommendationTests"
               BuildableName = "RecommendationTests"
               BlueprintName = "RecommendationTests"
               ReferencedContainer = "container:Packages/Recommendation//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "TenantChooserTests"
               BuildableName = "TenantChooserTests"
               BlueprintName = "TenantChooserTests"
               ReferencedContainer = "container:Packages/TenantChooser//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "PushPromotionTests"
               BuildableName = "PushPromotionTests"
               BlueprintName = "PushPromotionTests"
               ReferencedContainer = "container:Packages/PushPromotion//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "ProductReviewDetailTests"
               BuildableName = "ProductReviewDetailTests"
               BlueprintName = "ProductReviewDetailTests"
               ReferencedContainer = "container:Packages/ProductReviewDetail//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "UICatalogTests"
               BuildableName = "UICatalogTests"
               BlueprintName = "UICatalogTests"
               ReferencedContainer = "container:Packages/UICatalog//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "InboxTests"
               BuildableName = "InboxTests"
               BlueprintName = "InboxTests"
               ReferencedContainer = "container:Packages/Inbox//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGAsyncImageTests"
               BuildableName = "OGAsyncImageTests"
               BlueprintName = "OGAsyncImageTests"
               ReferencedContainer = "container:Packages/OGAsyncImage//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "ExternalDependenciesTests"
               BuildableName = "ExternalDependenciesTests"
               BlueprintName = "ExternalDependenciesTests"
               ReferencedContainer = "container:Packages/ExternalDependencies//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "ProductDetailTests"
               BuildableName = "ProductDetailTests"
               BlueprintName = "ProductDetailTests"
               ReferencedContainer = "container:Packages/ProductDetail//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "AppCoreTests"
               BuildableName = "AppCoreTests"
               BlueprintName = "AppCoreTests"
               ReferencedContainer = "container:Packages/AppCore//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "TabBarTests"
               BuildableName = "TabBarTests"
               BlueprintName = "TabBarTests"
               ReferencedContainer = "container:Packages/TabBar//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "DealsTests"
               BuildableName = "DealsTests"
               BlueprintName = "DealsTests"
               ReferencedContainer = "container:Packages/Deals//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "StoreFinderTests"
               BuildableName = "StoreFinderTests"
               BlueprintName = "StoreFinderTests"
               ReferencedContainer = "container:Packages/StoreFinder//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "WelcomeTests"
               BuildableName = "WelcomeTests"
               BlueprintName = "WelcomeTests"
               ReferencedContainer = "container:Packages/Welcome//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "SearchTests"
               BuildableName = "SearchTests"
               BlueprintName = "SearchTests"
               ReferencedContainer = "container:Packages/Search//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "AppUpdateTests"
               BuildableName = "AppUpdateTests"
               BlueprintName = "AppUpdateTests"
               ReferencedContainer = "container:Packages/AppUpdate//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "PromoBannerTests"
               BuildableName = "PromoBannerTests"
               BlueprintName = "PromoBannerTests"
               ReferencedContainer = "container:Packages/PromoBanner//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "TenantChooserCoreTests"
               BuildableName = "TenantChooserCoreTests"
               BlueprintName = "TenantChooserCoreTests"
               ReferencedContainer = "container:Packages/TenantChooserCore//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "ReviewTests"
               BuildableName = "ReviewTests"
               BlueprintName = "ReviewTests"
               ReferencedContainer = "container:Packages/Review//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGAppTests"
               BuildableName = "OGAppTests"
               BlueprintName = "OGAppTests"
               ReferencedContainer = "container:Packages/OGApp//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "NativeAPITests"
               BuildableName = "NativeAPITests"
               BlueprintName = "NativeAPITests"
               ReferencedContainer = "container:Packages/NativeAPI//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "AssortmentTests"
               BuildableName = "AssortmentTests"
               BlueprintName = "AssortmentTests"
               ReferencedContainer = "container:Packages/Assortment//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "TenantSwitchTests"
               BuildableName = "TenantSwitchTests"
               BlueprintName = "TenantSwitchTests"
               ReferencedContainer = "container:Packages/TenantSwitch//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "AccountTests"
               BuildableName = "AccountTests"
               BlueprintName = "AccountTests"
               ReferencedContainer = "container:Packages/Account//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "CatalogScannerTests"
               BuildableName = "CatalogScannerTests"
               BlueprintName = "CatalogScannerTests"
               ReferencedContainer = "container:Packages/CatalogScanner//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "LoginTests"
               BuildableName = "LoginTests"
               BlueprintName = "LoginTests"
               ReferencedContainer = "container:Packages/Login//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "BraFittingGuideTests"
               BuildableName = "BraFittingGuideTests"
               BlueprintName = "BraFittingGuideTests"
               ReferencedContainer = "container:Packages/BraFittingGuide//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "SalutationTests"
               BuildableName = "SalutationTests"
               BlueprintName = "SalutationTests"
               ReferencedContainer = "container:Packages/Salutation//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "AppTrackerTests"
               BuildableName = "AppTrackerTests"
               BlueprintName = "AppTrackerTests"
               ReferencedContainer = "container:Packages/AppTracker//">
            </BuildableReference>
         </TestableReference>
      </Testables>
      <CommandLineArguments>
      </CommandLineArguments>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Alpha (Debug)"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "8305ECA24D262C3BB9681D98"
            BuildableName = "PackagesTestHosts.app"
            BlueprintName = "PackagesTestHosts"
            ReferencedContainer = "container:OttoGroup.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
      </CommandLineArguments>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "8305ECA24D262C3BB9681D98"
            BuildableName = "PackagesTestHosts.app"
            BlueprintName = "PackagesTestHosts"
            ReferencedContainer = "container:OttoGroup.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
      </CommandLineArguments>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Alpha">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
