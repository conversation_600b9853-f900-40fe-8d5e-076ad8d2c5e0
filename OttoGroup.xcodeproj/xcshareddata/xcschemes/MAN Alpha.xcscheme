<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1600"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES"
      runPostActionsOnFailure = "NO">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "NO"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "77008908BEFC746572E44589"
               BuildableName = "MAN.app"
               BlueprintName = "MAN"
               ReferencedContainer = "container:OttoGroup.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "NO"
            buildForProfiling = "NO"
            buildForArchiving = "NO"
            buildForAnalyzing = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "450CD52F17833F16099A9B5F"
               BuildableName = "MAN-Tests.xctest"
               BlueprintName = "MAN-Tests"
               ReferencedContainer = "container:OttoGroup.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Alpha"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      onlyGenerateCoverageForSpecifiedTargets = "NO">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "450CD52F17833F16099A9B5F"
            BuildableName = "MAN-Tests.xctest"
            BlueprintName = "MAN-Tests"
            ReferencedContainer = "container:OttoGroup.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <Testables>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "450CD52F17833F16099A9B5F"
               BuildableName = "MAN-Tests.xctest"
               BlueprintName = "MAN-Tests"
               ReferencedContainer = "container:OttoGroup.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
      <CommandLineArguments>
      </CommandLineArguments>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Alpha"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "77008908BEFC746572E44589"
            BuildableName = "MAN.app"
            BlueprintName = "MAN"
            ReferencedContainer = "container:OttoGroup.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
         <CommandLineArgument
            argument = "-FIRDebugDisabled"
            isEnabled = "YES">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-FIRDebugEnabled"
            isEnabled = "NO">
         </CommandLineArgument>
      </CommandLineArguments>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Alpha"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "77008908BEFC746572E44589"
            BuildableName = "MAN.app"
            BlueprintName = "MAN"
            ReferencedContainer = "container:OttoGroup.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
      </CommandLineArguments>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Alpha">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Alpha"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
