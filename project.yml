attributes:
  ORGANIZATIONNAME: Otto Group
configs:
  Alpha: debug
  Alpha (Debug): debug
  Beta: release
  Release: release
include:
- base.yml
- packages.yml
- targets.yml
name: OttoGroup
options:
  bundleIdPrefix: com.ottogroup
  createIntermediateGroups: true
  defaultConfig: Alpha (Debug)
  deploymentTarget:
    iOS: '15.0'
  generateEmptyDirectories: true
  groupSortPosition: top
  indentWidth: 2
  minimumXcodeGenVersion: 2.44.1
  tabWidth: 2
  useBaseInternationalization: false
  usesTabs: false
  xcodeVersion: '16.0'
schemes:
  PackagesTestHosts:
    build:
      targets:
        PackagesTestHosts:
        - testing
    profile:
      config: Release
    run:
      config: Alpha (Debug)
    test:
      config: Alpha (Debug)
      targets:
      - package: Recommendation/RecommendationTests
      - package: TenantChooser/TenantChooserTests
      - package: PushPromotion/PushPromotionTests
      - package: ProductReviewDetail/ProductReviewDetailTests
      - package: UICatalog/UICatalogTests
      - package: Inbox/InboxTests
      - package: OGAsyncImage/OGAsyncImageTests
      - package: ExternalDependencies/ExternalDependenciesTests
      - package: ProductDetail/ProductDetailTests
      - package: AppCore/AppCoreTests
      - package: TabBar/TabBarTests
      - package: Deals/DealsTests
      - package: StoreFinder/StoreFinderTests
      - package: Welcome/WelcomeTests
      - package: Search/SearchTests
      - package: AppUpdate/AppUpdateTests
      - package: PromoBanner/PromoBannerTests
      - package: TenantChooserCore/TenantChooserCoreTests
      - package: Review/ReviewTests
      - package: OGApp/OGAppTests
      - package: NativeAPI/NativeAPITests
      - package: Assortment/AssortmentTests
      - package: TenantSwitch/TenantSwitchTests
      - package: Account/AccountTests
      - package: CatalogScanner/CatalogScannerTests
      - package: Login/LoginTests
      - package: BraFittingGuide/BraFittingGuideTests
      - package: Salutation/SalutationTests
      - package: AppTracker/AppTrackerTests
      - package: OGDialogCoordinator/OGDialogCoordinatorTests
      - package: OGURLCredentialStorage/OGURLCredentialStorageTests
      - package: OGBadge/OGBadgeTests
      - package: OGUserCore/OGUserCoreTests
      - package: OGDomainStore/OGDomainStoreTests
      - package: OGNavigation/OGNavigationTests
      - package: OGBundledFeatureSetFetcher/OGBundledFeatureSetFetcherTests
      - package: OGRemoteFeatureSetFetcher/OGRemoteFeatureSetFetcherTests
      - package: OGFeatureCore/OGFeatureCoreTests
      - package: OGFeatureAdapter/OGFeatureAdapterTests
      - package: OGTenantSwitch/OGTenantSwitchTests
      - package: OGFeatureConfigView/OGFeatureConfigViewTests
      - package: OGTenantCore/OGTenantCoreTests
      - package: OGTestEnvironmentKit/OGTestEnvironmentKitTests
      - package: OGTenantKit/OGTenantKitTests
      - package: OGHTTPClient/OGHTTPClientTests
      - package: OGWebBridge/OGWebBridgeTests
      - package: OGAppLifecycle/OGAppLifecycleTests
      - package: OGDetectiveComponent/OGDetectiveComponentTests
      - package: OGInAppBrowser/OGInAppBrowserTests
      - package: OGDeepLinkHandler/OGDeepLinkHandlerTests
      - package: OGExternalDependencies/OGExternalDependenciesTests
      - package: OGCopyCodeBanner/OGCopyCodeBannerTests
      - package: OGViewStore/OGViewStoreTests
      - package: OGAdjustReporter/OGAdjustReporterTests
      - package: OGAirshipReporter/OGAirshipReporterTests
      - package: OGFirebaseReporter/OGFirebaseReporterTests
      - package: OGTrackerCore/OGTrackerCoreTests
      - package: OGWebBridgeTracker/OGWebBridgeTrackerTests
      - package: OGTrackerOptInService/OGTrackerOptInServiceTests
      - package: OGTracker/OGTrackerTests
      - package: OGRouter/OGRouterTests
      - package: OGL10n/OGL10nTests
      - package: OGZipArchiver/OGZipArchiverTests
      - package: OGStorageTestsUtils/OGStorageTests
      - package: OGIdentifierTestsUtils/OGIdentifierTests
      - package: OGMacros/OGMacrosTests
      - package: OGCore/OGCoreTests
      - package: OGFirebaseKit/OGFirebaseKitTests
      - package: OGWebView/OGWebViewTests
      - package: OGSecret/OGSecretTests
      - package: OGNavigationBar/OGNavigationBarTests
      - package: OGSalutation/OGSalutationTests
      - package: OGAirshipKit/OGAirshipKitTests
      - package: OGLoginButton/OGLoginButtonTests
      - package: OGUserAgent/OGUserAgentTests
      - package: OGSystemKit/OGSystemKitTests
      - package: OGSearch/OGSearchTests
      - package: OGNetworkLogger/OGNetworkLoggerTests
      - package: OGExternalBrowser/OGExternalBrowserTests
settings:
  base:
    DEBUG_INFORMATION_FORMAT: dwarf-with-dsym
    SWIFT_VERSION: '5.9'
    TARGETED_DEVICE_FAMILY: 1,2
    VERSIONING_SYSTEM: apple-generic
  configs:
    Alpha:
      ALWAYS_SEARCH_USER_PATHS: false
      CODE_SIGN_STYLE: Manual
      ENABLE_BITCODE: false
      GCC_PREPROCESSOR_DEFINITIONS:
      - DEBUG=1
      - LOGGER=1
      OTHER_SWIFT_FLAGS: -DDEBUG -DLOGGER
      SWIFT_ACTIVE_COMPILATION_CONDITIONS: DEBUG
      SWIFT_COMPILATION_MODE: wholemodule
      SWIFT_OPTIMIZATION_LEVEL: -O
    Alpha (Debug):
      ALWAYS_SEARCH_USER_PATHS: false
      CODE_SIGN_STYLE: Automatic
      ENABLE_BITCODE: false
      GCC_PREPROCESSOR_DEFINITIONS:
      - DEBUG=1
      - LOGGER=1
      OTHER_SWIFT_FLAGS: -DDEBUG -DLOGGER
      SWIFT_ACTIVE_COMPILATION_CONDITIONS: DEBUG
      SWIFT_OPTIMIZATION_LEVEL: -Onone
    Beta:
      ALWAYS_SEARCH_USER_PATHS: false
      CODE_SIGN_STYLE: Manual
      ENABLE_BITCODE: false
      GCC_PREPROCESSOR_DEFINITIONS:
      - BETA=1
      - DEBUG=1
      - LOGGER=1
      OTHER_SWIFT_FLAGS: -DBETA -DDEBUG -DLOGGER
      SWIFT_COMPILATION_MODE: wholemodule
      SWIFT_OPTIMIZATION_LEVEL: -O
    Release:
      ALWAYS_SEARCH_USER_PATHS: false
      CODE_SIGN_STYLE: Manual
      ENABLE_BITCODE: false
      SWIFT_COMPILATION_MODE: wholemodule
      SWIFT_OPTIMIZATION_LEVEL: -Owholemodule
targets:
  PackagesTestHosts:
    dependencies:
    - package: Recommendation
    - package: TenantChooser
    - package: PushPromotion
    - package: ProductReviewDetail
    - package: UICatalog
    - package: Inbox
    - package: OGAsyncImage
    - package: ExternalDependencies
    - package: ProductDetail
    - package: AppCore
    - package: TabBar
    - package: Deals
    - package: StoreFinder
    - package: Welcome
    - package: Search
    - package: AppUpdate
    - package: PromoBanner
    - package: TenantChooserCore
    - package: Review
    - package: OGApp
    - package: NativeAPI
    - package: Assortment
    - package: TenantSwitch
    - package: Account
    - package: CatalogScanner
    - package: Login
    - package: BraFittingGuide
    - package: Salutation
    - package: AppTracker
    - package: OGDialogCoordinator
    - package: OGURLCredentialStorage
    - package: OGBadge
    - package: OGUserCore
    - package: OGDomainStore
    - package: OGNavigation
    - package: OGBundledFeatureSetFetcher
    - package: OGRemoteFeatureSetFetcher
    - package: OGFeatureCore
    - package: OGFeatureAdapter
    - package: OGTenantSwitch
    - package: OGFeatureConfigView
    - package: OGTenantCore
    - package: OGTestEnvironmentKit
    - package: OGTenantKit
    - package: OGHTTPClient
    - package: OGWebBridge
    - package: OGAppLifecycle
    - package: OGDetectiveComponent
    - package: OGInAppBrowser
    - package: OGDeepLinkHandler
    - package: OGExternalDependencies
    - package: OGCopyCodeBanner
    - package: OGViewStore
    - package: OGAdjustReporter
    - package: OGAirshipReporter
    - package: OGFirebaseReporter
    - package: OGTrackerCore
    - package: OGWebBridgeTracker
    - package: OGTrackerOptInService
    - package: OGTracker
    - package: OGRouter
    - package: OGL10n
    - package: OGZipArchiver
    - package: OGStorageTestsUtils
    - package: OGIdentifierTestsUtils
    - package: OGMacros
    - package: OGCore
    - package: OGFirebaseKit
    - package: OGWebView
    - package: OGSecret
    - package: OGNavigationBar
    - package: OGSalutation
    - package: OGAirshipKit
    - package: OGLoginButton
    - package: OGUserAgent
    - package: OGSystemKit
    - package: OGSearch
    - package: OGNetworkLogger
    - package: OGExternalBrowser
    platform: iOS
    sources:
    - path: PackagesTestHosts/Sources
    - path: PackagesTestHosts/Info.plist
    type: application
